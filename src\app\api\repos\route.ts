import { NextRequest, NextResponse } from 'next/server';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '@/../convex/_generated/api';

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export async function POST(req: NextRequest) {
	try {
		const { workspaceId } = await req.json();

		if (!workspaceId) {
			return NextResponse.json(
				{ error: 'Workspace ID is required' },
				{ status: 400 }
			);
		}

		try {
			// Query the GitHub repository integration for this workspace
			const repo = await convex.query(api.integrations.getGitHubRepository, {
				workspaceId,
			});

			return NextResponse.json(repo);
		} catch (convexError) {
			console.log('No GitHub repository found for workspace:', workspaceId);
			// Return null if no repository is connected
			return NextResponse.json(null);
		}
	} catch (error) {
		console.error('Error fetching repository:', error);
		return NextResponse.json(
			{ error: 'Failed to fetch repository' },
			{ status: 500 }
		);
	}
}
